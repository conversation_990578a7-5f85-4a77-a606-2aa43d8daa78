__int64 SFSController__GAAPMEDFDFL()
{
  __int64 v0; // x20
  __int64 v1; // x0
  __int64 InventoryRaw; // x0
  __int64 v3; // x0
  __int64 v4; // x0
  __int64 v5; // x0
  __int64 v6; // x0
  __int64 v7; // x0
  __int64 StorageRaw; // x0
  __int64 v9; // x0
  __int64 AccountRaw; // x0
  __int64 v11; // x0
  __int64 Quests; // x0

  if ( (byte_490BC57 & 1) == 0 )
  {
    sub_184E408(&Sfs2X_Entities_Data_SFSObject_TypeInfo);
    sub_184E408(&StringLiteral_14484);
    sub_184E408(&StringLiteral_14228);
    sub_184E408(&StringLiteral_14496);
    sub_184E408(&StringLiteral_18444);
    sub_184E408(&StringLiteral_14491);
    sub_184E408(&StringLiteral_14037);
    byte_490BC57 = 1;
  }
  v0 = sub_184E624(Sfs2X_Entities_Data_SFSObject_TypeInfo);
  Sfs2X_Entities_Data_SFSObject___ctor(v0, 0LL);
  if ( !byte_490BC65 )
  {
    sub_184E408(&SaveController_TypeInfo);
    byte_490BC65 = 1;
  }
  v1 = **((_QWORD **)SaveController_TypeInfo + 23);
  if ( !v1 )
    sub_184E634(0LL);
  InventoryRaw = SaveController__LoadInventoryRaw(v1, 0LL, 0LL);
  if ( !v0 )
    sub_184E634(InventoryRaw);
  Sfs2X_Entities_Data_SFSObject__PutSFSObject(v0, StringLiteral_14484, InventoryRaw, 0LL);
  if ( !byte_490BC65 )
  {
    sub_184E408(&SaveController_TypeInfo);
    byte_490BC65 = 1;
  }
  v3 = **((_QWORD **)SaveController_TypeInfo + 23);
  if ( !v3 )
    sub_184E634(0LL);
  v4 = SaveController__LoadInventoryRaw(v3, 1LL, 0LL);
  Sfs2X_Entities_Data_SFSObject__PutSFSObject(v0, StringLiteral_14491, v4, 0LL);
  if ( !byte_490BC65 )
  {
    sub_184E408(&SaveController_TypeInfo);
    byte_490BC65 = 1;
  }
  v5 = **((_QWORD **)SaveController_TypeInfo + 23);
  if ( !v5 )
    sub_184E634(0LL);
  v6 = SaveController__LoadInventoryRaw(v5, 2LL, 0LL);
  Sfs2X_Entities_Data_SFSObject__PutSFSObject(v0, StringLiteral_14496, v6, 0LL);
  if ( !byte_490BC65 )
  {
    sub_184E408(&SaveController_TypeInfo);
    byte_490BC65 = 1;
  }
  v7 = **((_QWORD **)SaveController_TypeInfo + 23);
  if ( !v7 )
    sub_184E634(0LL);
  StorageRaw = SaveController__LoadStorageRaw(v7, 0LL);
  Sfs2X_Entities_Data_SFSObject__PutIntArray(v0, StringLiteral_14228, StorageRaw, 0LL);
  if ( !byte_490BC65 )
  {
    sub_184E408(&SaveController_TypeInfo);
    byte_490BC65 = 1;
  }
  v9 = **((_QWORD **)SaveController_TypeInfo + 23);
  if ( !v9 )
    sub_184E634(0LL);
  AccountRaw = SaveController__LoadAccountRaw(v9, 0LL);
  Sfs2X_Entities_Data_SFSObject__PutSFSObject(v0, StringLiteral_14037, AccountRaw, 0LL);
  if ( !byte_490BC65 )
  {
    sub_184E408(&SaveController_TypeInfo);
    byte_490BC65 = 1;
  }
  v11 = **((_QWORD **)SaveController_TypeInfo + 23);
  if ( !v11 )
    sub_184E634(0LL);
  Quests = SaveController__LoadQuests(v11, 0LL);
  Sfs2X_Entities_Data_SFSObject__PutUtfString(v0, StringLiteral_18444, Quests, 0LL);
  return v0;
}