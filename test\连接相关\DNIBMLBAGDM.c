_QWORD *FGHFGKNJOFD__DNIBMLBAGDM()
{
  _QWORD *FGHFGKNJOFD_TypeInfo; // x0
  __int64 v1; // x8
  unsigned __int64 v2; // x20
  __int64 v3; // x8
  unsigned __int64 i; // x20
  __int64 v5; // x8
  __int64 v6; // x8
  __int64 v7; // x8

  if ( (byte_49104F5 & 1) == 0 )
  {
    sub_184E408(&FGHFGKNJOFD_TypeInfo);
    byte_49104F5 = 1;
  }
  FGHFGKNJOFD_TypeInfo = FGHFGKNJOFD_TypeInfo;
  if ( !*((_DWORD *)FGHFGKNJOFD_TypeInfo + 56) )
  {
    j_il2cpp_runtime_class_init_0(FGHFGKNJOFD_TypeInfo);
    FGHFGKNJOFD_TypeInfo = FGHFGKNJOFD_TypeInfo;
  }
  v1 = FGHFGKNJOFD_TypeInfo[23];
  if ( *(_BYTE *)(v1 + 80) )
  {
    if ( !*((_DWORD *)FGHFGKNJOFD_TypeInfo + 56) )
    {
      j_il2cpp_runtime_class_init_0(FGHFGKNJOFD_TypeInfo);
      FGHFGKNJOFD_TypeInfo = FGHFGKNJOFD_TypeInfo;
      v1 = *((_QWORD *)FGHFGKNJOFD_TypeInfo + 23);
    }
    v2 = 0LL;
    *(_BYTE *)(v1 + 80) = 0;
    while ( 1 )
    {
      if ( !*((_DWORD *)FGHFGKNJOFD_TypeInfo + 56) )
      {
        j_il2cpp_runtime_class_init_0(FGHFGKNJOFD_TypeInfo);
        FGHFGKNJOFD_TypeInfo = FGHFGKNJOFD_TypeInfo;
      }
      v3 = *(_QWORD *)(FGHFGKNJOFD_TypeInfo[23] + 16LL);
      if ( !v3 )
        goto LABEL_27;
      if ( (__int64)v2 >= *(int *)(v3 + 24) )
        break;
      if ( !*((_DWORD *)FGHFGKNJOFD_TypeInfo + 56) )
      {
        FGHFGKNJOFD_TypeInfo = (_QWORD *)j_il2cpp_runtime_class_init_0(FGHFGKNJOFD_TypeInfo);
        v3 = *(_QWORD *)(*((_QWORD *)FGHFGKNJOFD_TypeInfo + 23) + 16LL);
        if ( !v3 )
          goto LABEL_27;
      }
      if ( v2 >= *(unsigned int *)(v3 + 24) )
LABEL_28:
        sub_184E63C();
      *(_QWORD *)(v3 + 8 * v2 + 32) = 0LL;
      FGHFGKNJOFD_TypeInfo = FGHFGKNJOFD_TypeInfo;
      ++v2;
    }
    for ( i = 0LL; ; ++i )
    {
      if ( !*((_DWORD *)FGHFGKNJOFD_TypeInfo + 56) )
      {
        j_il2cpp_runtime_class_init_0(FGHFGKNJOFD_TypeInfo);
        FGHFGKNJOFD_TypeInfo = FGHFGKNJOFD_TypeInfo;
      }
      v5 = *(_QWORD *)(FGHFGKNJOFD_TypeInfo[23] + 24LL);
      if ( !v5 )
        break;
      if ( (__int64)i >= *(int *)(v5 + 24) )
        return FGHFGKNJOFD_TypeInfo;
      if ( !*((_DWORD *)FGHFGKNJOFD_TypeInfo + 56) )
      {
        j_il2cpp_runtime_class_init_0(FGHFGKNJOFD_TypeInfo);
        FGHFGKNJOFD_TypeInfo = FGHFGKNJOFD_TypeInfo;
      }
      v6 = *(_QWORD *)(FGHFGKNJOFD_TypeInfo[23] + 24LL);
      if ( !v6 )
        break;
      if ( i >= *(unsigned int *)(v6 + 24) )
        goto LABEL_28;
      v7 = v6 + 4 * i;
      *(_DWORD *)(v7 + 32) = 0;
    }
LABEL_27:
    sub_184E634(FGHFGKNJOFD_TypeInfo);
  }
  return FGHFGKNJOFD_TypeInfo;
}