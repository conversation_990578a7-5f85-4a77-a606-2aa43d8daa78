__int64 __fastcall SFSController__IENIOAABKAL(__int64 a1, __int64 a2)
{
  _QWORD *instance; // x0
  __int64 v5; // x8
  __int64 v6; // x8
  __int64 v7; // x21
  __int64 v8; // x8
  _QWORD *instance_1; // x20
  __int64 v10; // x9
  __int64 StringLiteral_15595; // x22
  int *v12; // x10
  __int64 v13; // x0
  __int64 v14; // x0
  __int64 result; // x0
  __int64 v16; // x8
  __int64 v17; // x0

  if ( (byte_490BC22 & 1) == 0 )
  {
    sub_184E408(&GameController_TypeInfo);
    sub_184E408(&System_Collections_Generic_IDictionary_string__object__TypeInfo);
    sub_184E408(&string_TypeInfo);
    sub_184E408(&StringLiteral_15595);
    sub_184E408(&StringLiteral_4781);
    byte_490BC22 = 1;
  }
  if ( (CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
          *(_QWORD *)(a1 + 752),
          *(unsigned int *)(a1 + 760),
          0LL) & 1) != 0 )
  {
    if ( !*((_DWORD *)GameController_TypeInfo + 56) )
      j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
    instance = (_QWORD *)GameController__get_instance(0LL);
    if ( !instance )
      goto LABEL_33;
    v5 = instance[12];
    if ( !v5 )
      goto LABEL_33;
    instance = *(_QWORD **)(v5 + 160);
    if ( !instance )
      goto LABEL_33;
    UI_Message__Show(instance, StringLiteral_4781, 1LL, 0LL);
  }
  if ( !*((_DWORD *)GameController_TypeInfo + 56) )
    j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
  instance = (_QWORD *)GameController__get_instance(0LL);
  if ( !instance )
    goto LABEL_33;
  v6 = instance[12];
  if ( !v6 )
    goto LABEL_33;
  if ( !a2 )
    goto LABEL_33;
  v7 = *(_QWORD *)(v6 + 160);
  instance = (_QWORD *)Sfs2X_Core_BaseEvent__get_Params(a2, 0LL);
  if ( !instance )
    goto LABEL_33;
  v8 = *instance;
  instance_1 = instance;
  v10 = *(unsigned __int16 *)(*instance + 302LL);
  StringLiteral_15595 = StringLiteral_15595;
  if ( *(_WORD *)(*instance + 302LL) )
  {
    v12 = (int *)(*(_QWORD *)(v8 + 176) + 8LL);
    while ( *((_QWORD *)v12 - 1) != System_Collections_Generic_IDictionary_string__object__TypeInfo )
    {
      --v10;
      v12 += 4;
      if ( !v10 )
        goto LABEL_20;
    }
    v13 = v8 + 16LL * *v12 + 312;
  }
  else
  {
LABEL_20:
    v13 = sub_185BD18(instance, System_Collections_Generic_IDictionary_string__object__TypeInfo, 0LL);
  }
  instance = (_QWORD *)(*(__int64 (__fastcall **)(_QWORD *, __int64, _QWORD))v13)(
                         instance_1,
                         StringLiteral_15595,
                         *(_QWORD *)(v13 + 8));
  if ( !v7 )
    goto LABEL_33;
  if ( instance && (_UNKNOWN *)*instance != string_TypeInfo )
  {
    v17 = sub_184E9B4(instance);
    return SFSController___cctor(v17);
  }
  UI_Message__Show(v7, instance, 1LL, 0LL);
  v14 = *(_QWORD *)(a1 + 64);
  if ( v14 )
    Sfs2X_SmartFox__Disconnect(v14, 0LL);
  SFSController__DNIBMLBAGDM(a1, 1LL);
  if ( !*((_DWORD *)GameController_TypeInfo + 56) )
    j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
  instance = (_QWORD *)GameController__get_instance(0LL);
  if ( !instance || (instance = (_QWORD *)instance[15]) == 0LL )
LABEL_33:
    sub_184E634(instance);
  result = NetworkManager__set_ErrorConnectingSFS(instance, 1LL, 0LL);
  v16 = *(_QWORD *)(a1 + 104);
  if ( v16 )
    return (*(__int64 (__fastcall **)(_QWORD, _QWORD, _QWORD))(v16 + 24))(
             *(_QWORD *)(v16 + 64),
             0LL,
             *(_QWORD *)(v16 + 40));
  return result;
}