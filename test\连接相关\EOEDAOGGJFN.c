// SFS (SmartFoxServer) 控制器函数 - 处理网络连接事件
__int64 __fastcall SFSController__EOEDAOGGJFN(_QWORD *Params, __int64 a2)
{
  _QWORD *Params_1; // x19 - 保存原始参数指针
  __int64 v4; // x8
  _QWORD *Params_2; // x20 - 事件参数副本
  __int64 v6; // x9
  __int64 StringLiteral_19257; // x21 - 字符串字面量
  int *v8; // x10
  __int64 v9; // x0
  __int64 v10; // x0
  __int64 result; // x0
  __int64 v12; // x8
  __int64 v13; // x8
  __int64 v14; // x8
  __int64 v15; // x0

  Params_1 = Params; // 保存控制器实例指针

  // 静态初始化检查 - 确保类型信息只初始化一次
  if ( (byte_490BBE6 & 1) == 0 )
  {
    // 初始化各种类型信息和字符串字面量
    sub_184E408(&bool_TypeInfo);
    sub_184E408(&GameController_TypeInfo);
    sub_184E408(&System_Collections_Generic_IDictionary_string__object__TypeInfo);
    sub_184E408(&StringLiteral_19257);
    sub_184E408(&StringLiteral_4787);
    Params = (_QWORD *)sub_184E408(&StringLiteral_4772);
    byte_490BBE6 = 1; // 标记已初始化
  }

  // 检查事件对象是否有效
  if ( !a2 )
    goto LABEL_37; // 事件为空，跳转到错误处理

  // 获取事件参数字典
  Params = (_QWORD *)Sfs2X_Core_BaseEvent__get_Params(a2, 0LL);
  if ( !Params )
    goto LABEL_37; // 参数为空，跳转到错误处理

  v4 = *Params;
  Params_2 = Params; // 保存参数指针
  v6 = *(unsigned __int16 *)(*Params + 302LL); // 获取字段数量
  StringLiteral_19257 = StringLiteral_19257;

  // 在参数字典中查找特定类型的字段
  if ( *(_WORD *)(*Params + 302LL) )
  {
    // 遍历字段数组，寻找IDictionary类型
    v8 = (int *)(*(_QWORD *)(v4 + 176) + 8LL);
    while ( *((_QWORD *)v8 - 1) != System_Collections_Generic_IDictionary_string__object__TypeInfo )
    {
      --v6;
      v8 += 4;
      if ( !v6 )
        goto LABEL_9; // 未找到，使用备用方法
    }
    v9 = v4 + 16LL * *v8 + 312; // 计算字段地址
  }
  else
  {
LABEL_9:
    // 备用方法：通过类型查找获取字段
    v9 = sub_185BD18(Params, System_Collections_Generic_IDictionary_string__object__TypeInfo, 0LL);
  }

  // 调用字典的查找方法，获取特定键的值
  Params = (_QWORD *)(*(__int64 (__fastcall **)(_QWORD *, __int64, _QWORD))v9)(
                       Params_2,
                       StringLiteral_19257,
                       *(_QWORD *)(v9 + 8));
  if ( !Params )
    goto LABEL_37; // 未找到指定键值，跳转到错误处理
  // 检查返回值是否为布尔类型
  if ( *(_QWORD *)(*Params + 64LL) == *((_QWORD *)bool_TypeInfo + 8) )
  {
    // 解包布尔值并检查其值
    if ( *(_BYTE *)j_il2cpp_object_unbox_0() )
    {
      // 布尔值为true的情况 - 连接成功
      if ( *((_BYTE *)Params_1 + 96) ) // 检查是否需要加密
      {
        v10 = Params_1[8]; // 获取SmartFox实例
        if ( v10 )
          return Sfs2X_SmartFox__InitCrypto(v10, 0LL); // 初始化加密
      }

      // 执行连接成功后的处理
      SFSController__HCAJEPAGHLL(Params_1);

      // 检查反作弊系统的布尔值
      result = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
                 Params_1[94],
                 *((unsigned int *)Params_1 + 190),
                 0LL);
      if ( (result & 1) != 0 )
      {
        // 获取游戏控制器实例并显示成功消息
        if ( !*((_DWORD *)GameController_TypeInfo + 56) )
          j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
        Params = (_QWORD *)GameController__get_instance(0LL);
        if ( Params )
        {
          v12 = Params[12]; // 获取UI管理器
          if ( v12 )
          {
            Params = *(_QWORD **)(v12 + 160); // 获取消息组件
            if ( Params )
              return UI_Message__Show(Params, StringLiteral_4772, 1LL, 0LL); // 显示成功消息
          }
        }
LABEL_37:
        sub_184E634(Params); // 错误处理/清理函数
      }
    }
    else
    {
      // 布尔值为false的情况 - 连接失败
      if ( (CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
              Params_1[94],
              *((unsigned int *)Params_1 + 190),
              0LL) & 1) != 0 )
      {
        // 显示连接失败消息
        if ( !*((_DWORD *)GameController_TypeInfo + 56) )
          j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
        Params = (_QWORD *)GameController__get_instance(0LL);
        if ( !Params )
          goto LABEL_37;
        v13 = Params[12]; // 获取UI管理器
        if ( !v13 )
          goto LABEL_37;
        Params = *(_QWORD **)(v13 + 160); // 获取消息组件
        if ( !Params )
          goto LABEL_37;
        UI_Message__Show(Params, StringLiteral_4787, 1LL, 0LL); // 显示失败消息
      }

      // 执行连接失败后的清理工作
      SFSController__DNIBMLBAGDM(Params_1, 1LL);

      // 设置网络错误状态
      if ( !*((_DWORD *)GameController_TypeInfo + 56) )
        j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
      Params = (_QWORD *)GameController__get_instance(0LL);
      if ( !Params )
        goto LABEL_37;
      Params = (_QWORD *)Params[15]; // 获取网络管理器
      if ( !Params )
        goto LABEL_37;
      result = NetworkManager__set_ErrorConnectingSFS(Params, 1LL, 0LL); // 设置SFS连接错误标志

      // 调用回调函数（如果存在）
      v14 = Params_1[15];
      if ( v14 )
        return (*(__int64 (__fastcall **)(_QWORD, _QWORD))(v14 + 24))(*(_QWORD *)(v14 + 64), *(_QWORD *)(v14 + 40));
    }
  }
  else
  {
    // 返回值不是布尔类型 - 处理异常情况
    v15 = sub_184E9B4(); // 获取错误信息
    return SFSController__DIDFCDEJNAI(v15); // 调用错误处理函数
  }
  return result;
}