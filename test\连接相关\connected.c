__int64 __fastcall SFSController__Connect(__int64 a1, __int64 a2, __int64 a3, char a4)
{
  __int64 v8; // x0
  __int64 instance; // x0
  __int64 v10; // x0
  __int64 v11; // x8
  __int64 *v12; // x8
  __int64 v13; // x8
  int v14; // w1
  __int64 v15; // x20
  _QWORD *SFSController_TypeInfo; // x0
  __int64 v17; // x21
  __int64 v18; // x0
  _QWORD *v19; // x8
  _QWORD *SFSController_TypeInfo_1; // x0
  __int64 v21; // x21
  _QWORD *Sfs2X.Core.SFSEvent_TypeInfo; // x0
  __int64 v23; // x21
  __int64 v24; // x22
  __int64 v25; // x23
  __int64 v26; // x22
  __int64 v27; // x21
  __int64 v28; // x23
  __int64 v29; // x22
  __int64 v30; // x21
  __int64 v31; // x23
  __int64 v32; // x22
  __int64 v33; // x21
  __int64 v34; // x23
  __int64 v35; // x22
  __int64 v36; // x21
  __int64 v37; // x23
  __int64 v38; // x22
  __int64 v39; // x21
  __int64 v40; // x23
  __int64 instance_1; // x0
  __int64 v43; // x8
  __int64 v44; // x8
  __int64 v45; // x8

  if ( (byte_490BBBC & 1) == 0 )
  {
    sub_184E408(&Sfs2X_Util_ConfigData_TypeInfo);
    sub_184E408(&Sfs2X_Core_EventListenerDelegate_TypeInfo);
    sub_184E408(&GameController_TypeInfo);
    sub_184E408(&Method_SFSController_EHJAGHMDHIC__);
    sub_184E408(&Method_SFSController_EIEACBBEFCI__);
    sub_184E408(&Method_SFSController_EOEDAOGGJFN__);
    sub_184E408(&Method_SFSController_FFIGKBKIKJH__);
    sub_184E408(&Method_SFSController_IENIOAABKAL__);
    sub_184E408(&Method_SFSController_PNJEOCPLAGP__);
    sub_184E408(&SFSController_TypeInfo);
    sub_184E408(&Sfs2X_Util_SFSErrorCodes_TypeInfo);
    sub_184E408(&Sfs2X_Core_SFSEvent_TypeInfo);
    sub_184E408(&Sfs2X_SmartFox_TypeInfo);
    sub_184E408(&StringLiteral_18958);
    sub_184E408(&StringLiteral_5412);
    sub_184E408(&StringLiteral_5413);
    sub_184E408(&StringLiteral_18956);
    sub_184E408(&StringLiteral_5331);
    sub_184E408(&StringLiteral_4787);
    sub_184E408(&StringLiteral_18957);
    sub_184E408(&StringLiteral_4778);
    sub_184E408(&StringLiteral_4789);
    sub_184E408(&StringLiteral_1);
    sub_184E408(&StringLiteral_5414);
    byte_490BBBC = 1;
  }
  v8 = *(_QWORD *)(a1 + 64);
  if ( !v8 )
    goto LABEL_16;
  if ( (Sfs2X_SmartFox__get_IsConnected(v8, 0LL) & 1) == 0 )
  {
    instance = *(_QWORD *)(a1 + 64);
    if ( !instance )
      goto LABEL_70;
    if ( (Sfs2X_SmartFox__get_IsConnecting(instance, 0LL) & 1) == 0 )
    {
LABEL_16:
      if ( (a4 & 1) != 0 )
      {
        if ( !*((_DWORD *)GameController_TypeInfo + 56) )
          j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
        instance = GameController__get_instance(0LL);
        if ( !instance )
          goto LABEL_70;
        v13 = *(_QWORD *)(instance + 96);
        if ( !v13 )
          goto LABEL_70;
        instance = *(_QWORD *)(v13 + 160);
        if ( !instance )
          goto LABEL_70;
        UI_Message__Show(instance, StringLiteral_4778, 1LL, 0LL);
      }
      *(_QWORD *)(a1 + 752) = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit(a4 & 1, 0LL);
      *(_DWORD *)(a1 + 760) = v14;
      *(_QWORD *)(a1 + 120) = a2;
      v15 = sub_184E624(Sfs2X_Util_ConfigData_TypeInfo);
      Sfs2X_Util_ConfigData___ctor(v15, 0LL);
      SFSController_TypeInfo = SFSController_TypeInfo;
      if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
      {
        j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
        SFSController_TypeInfo = SFSController_TypeInfo;
      }
      v17 = *(_QWORD *)(SFSController_TypeInfo[23] + 8LL);
      v18 = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit(StringLiteral_1, 0LL);
      if ( (CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Equality(v17, v18, 0LL) & 1) != 0 )
      {
        v19 = (_QWORD *)(a1 + 72);
      }
      else
      {
        SFSController_TypeInfo_1 = SFSController_TypeInfo;
        if ( !*((_DWORD *)SFSController_TypeInfo + 56) )
        {
          j_il2cpp_runtime_class_init_0(SFSController_TypeInfo);
          SFSController_TypeInfo_1 = SFSController_TypeInfo;
        }
        v19 = (_QWORD *)(SFSController_TypeInfo_1[23] + 8LL);
      }
      instance = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(*v19, 0LL);
      if ( v15 )
      {
        *(_QWORD *)(v15 + 16) = instance;
        *(_DWORD *)(v15 + 24) = *(_DWORD *)(a1 + 80);
        *(_QWORD *)(v15 + 48) = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(
                                  *(_QWORD *)(a1 + 88),
                                  0LL);
        v21 = sub_184E624(Sfs2X_SmartFox_TypeInfo);
        instance = Sfs2X_SmartFox___ctor(v21, 0LL);
        *(_QWORD *)(a1 + 64) = v21;
        if ( v21 )
        {
          Sfs2X_SmartFox__set_ThreadSafeMode(v21, 1LL, 0LL);
          instance = *(_QWORD *)(a1 + 64);
          if ( instance )
          {
            Sfs2X_SmartFox__RemoveAllEventListeners(instance, 0LL);
            Sfs2X.Core.SFSEvent_TypeInfo = Sfs2X_Core_SFSEvent_TypeInfo;
            v23 = *(_QWORD *)(a1 + 64);
            if ( !*((_DWORD *)Sfs2X_Core_SFSEvent_TypeInfo + 56) )
            {
              j_il2cpp_runtime_class_init_0(Sfs2X_Core_SFSEvent_TypeInfo);
              Sfs2X.Core.SFSEvent_TypeInfo = Sfs2X_Core_SFSEvent_TypeInfo;
            }
            v24 = *(_QWORD *)(Sfs2X.Core.SFSEvent_TypeInfo[23] + 16LL);
            v25 = sub_184E624(Sfs2X_Core_EventListenerDelegate_TypeInfo);
            instance = Sfs2X_Core_EventListenerDelegate___ctor(v25, a1, Method_SFSController_EOEDAOGGJFN__, 0LL);
            if ( v23 )
            {
              Sfs2X_SmartFox__AddEventListener(v23, v24, v25, 0LL);
              v26 = *(_QWORD *)(a1 + 64);
              v27 = *(_QWORD *)(*((_QWORD *)Sfs2X_Core_SFSEvent_TypeInfo + 23) + 40LL);
              v28 = sub_184E624(Sfs2X_Core_EventListenerDelegate_TypeInfo);
              instance = Sfs2X_Core_EventListenerDelegate___ctor(v28, a1, Method_SFSController_PNJEOCPLAGP__, 0LL);
              if ( v26 )
              {
                Sfs2X_SmartFox__AddEventListener(v26, v27, v28, 0LL);
                v29 = *(_QWORD *)(a1 + 64);
                v30 = *(_QWORD *)(*((_QWORD *)Sfs2X_Core_SFSEvent_TypeInfo + 23) + 88LL);
                v31 = sub_184E624(Sfs2X_Core_EventListenerDelegate_TypeInfo);
                instance = Sfs2X_Core_EventListenerDelegate___ctor(v31, a1, Method_SFSController_EIEACBBEFCI__, 0LL);
                if ( v29 )
                {
                  Sfs2X_SmartFox__AddEventListener(v29, v30, v31, 0LL);
                  v32 = *(_QWORD *)(a1 + 64);
                  v33 = *(_QWORD *)(*((_QWORD *)Sfs2X_Core_SFSEvent_TypeInfo + 23) + 96LL);
                  v34 = sub_184E624(Sfs2X_Core_EventListenerDelegate_TypeInfo);
                  instance = Sfs2X_Core_EventListenerDelegate___ctor(v34, a1, Method_SFSController_IENIOAABKAL__, 0LL);
                  if ( v32 )
                  {
                    Sfs2X_SmartFox__AddEventListener(v32, v33, v34, 0LL);
                    v35 = *(_QWORD *)(a1 + 64);
                    v36 = *(_QWORD *)(*((_QWORD *)Sfs2X_Core_SFSEvent_TypeInfo + 23) + 216LL);
                    v37 = sub_184E624(Sfs2X_Core_EventListenerDelegate_TypeInfo);
                    instance = Sfs2X_Core_EventListenerDelegate___ctor(v37, a1, Method_SFSController_FFIGKBKIKJH__, 0LL);
                    if ( v35 )
                    {
                      Sfs2X_SmartFox__AddEventListener(v35, v36, v37, 0LL);
                      v38 = *(_QWORD *)(a1 + 64);
                      v39 = *(_QWORD *)(*((_QWORD *)Sfs2X_Core_SFSEvent_TypeInfo + 23) + 408LL);
                      v40 = sub_184E624(Sfs2X_Core_EventListenerDelegate_TypeInfo);
                      instance = Sfs2X_Core_EventListenerDelegate___ctor(
                                   v40,
                                   a1,
                                   Method_SFSController_EHJAGHMDHIC__,
                                   0LL);
                      if ( v38 )
                      {
                        Sfs2X_SmartFox__AddEventListener(v38, v39, v40, 0LL);
                        if ( !*((_DWORD *)Sfs2X_Util_SFSErrorCodes_TypeInfo + 56) )
                          j_il2cpp_runtime_class_init_0(Sfs2X_Util_SFSErrorCodes_TypeInfo);
                        Sfs2X_Util_SFSErrorCodes__SetErrorMessage(6LL, StringLiteral_18956, 0LL);
                        Sfs2X_Util_SFSErrorCodes__SetErrorMessage(7LL, StringLiteral_18957, 0LL);
                        Sfs2X_Util_SFSErrorCodes__SetErrorMessage(0LL, StringLiteral_18958, 0LL);
                        Sfs2X_Util_SFSErrorCodes__SetErrorMessage(10LL, StringLiteral_18957, 0LL);
                        Sfs2X_Util_SFSErrorCodes__SetErrorMessage(28LL, StringLiteral_4787, 0LL);
                        *(_BYTE *)(a1 + 792) = 1;
                        MovementEffects_Timing__KillCoroutines_26616548(StringLiteral_5331, 0LL);
                        instance = *(_QWORD *)(a1 + 64);
                        if ( instance )
                        {
                          Sfs2X_SmartFox__Connect_63861312(instance, v15, 0LL);
                          return 1LL;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
LABEL_70:
      sub_184E634(instance);
    }
  }
  v10 = *(_QWORD *)(a1 + 64);
  if ( !v10 )
  {
LABEL_54:
    SFSController__DNIBMLBAGDM(a1, 1LL);
    if ( (a4 & 1) == 0 )
      goto LABEL_62;
    if ( !*((_DWORD *)GameController_TypeInfo + 56) )
      j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
    instance = GameController__get_instance(0LL);
    if ( instance )
    {
      v44 = *(_QWORD *)(instance + 96);
      if ( v44 )
      {
        instance = *(_QWORD *)(v44 + 160);
        if ( instance )
        {
          v12 = &StringLiteral_5414;
          goto LABEL_61;
        }
      }
    }
    goto LABEL_70;
  }
  if ( (Sfs2X_SmartFox__get_IsConnected(v10, 0LL) & 1) == 0 )
  {
    instance_1 = *(_QWORD *)(a1 + 64);
    if ( instance_1 && (Sfs2X_SmartFox__get_IsConnecting(instance_1, 0LL) & 1) != 0 )
    {
      SFSController__DNIBMLBAGDM(a1, 1LL);
      if ( (a4 & 1) == 0 )
        goto LABEL_62;
      if ( !*((_DWORD *)GameController_TypeInfo + 56) )
        j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
      instance = GameController__get_instance(0LL);
      if ( instance )
      {
        v43 = *(_QWORD *)(instance + 96);
        if ( v43 )
        {
          instance = *(_QWORD *)(v43 + 160);
          if ( instance )
          {
            v12 = &StringLiteral_5413;
            goto LABEL_61;
          }
        }
      }
      goto LABEL_70;
    }
    goto LABEL_54;
  }
  SFSController__Disconnect(a1);
  if ( (a4 & 1) == 0 )
    goto LABEL_62;
  if ( !*((_DWORD *)GameController_TypeInfo + 56) )
    j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
  instance = GameController__get_instance(0LL);
  if ( !instance )
    goto LABEL_70;
  v11 = *(_QWORD *)(instance + 96);
  if ( !v11 )
    goto LABEL_70;
  instance = *(_QWORD *)(v11 + 160);
  if ( !instance )
    goto LABEL_70;
  v12 = &StringLiteral_5412;
LABEL_61:
  UI_Message__Show(instance, *v12, 1LL, 0LL);
LABEL_62:
  if ( a3 )
    (*(void (__fastcall **)(_QWORD, _QWORD))(a3 + 24))(*(_QWORD *)(a3 + 64), *(_QWORD *)(a3 + 40));
  if ( !*((_DWORD *)GameController_TypeInfo + 56) )
    j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
  instance = GameController__get_instance(0LL);
  if ( !instance )
    goto LABEL_70;
  v45 = *(_QWORD *)(instance + 96);
  if ( !v45 )
    goto LABEL_70;
  instance = *(_QWORD *)(v45 + 160);
  if ( !instance )
    goto LABEL_70;
  UI_Message__Show(instance, StringLiteral_4789, 1LL, 0LL);
  return 0LL;
}