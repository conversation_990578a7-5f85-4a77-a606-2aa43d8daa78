__int64 __fastcall SFSController__ValidateNetwork(__int64 a1)
{
  __int64 DCEBNDFOAMB_TypeInfo_2; // x0
  _QWORD *DCEBNDFOAMB_TypeInfo; // x0
  _QWORD *DCEBNDFOAMB_TypeInfo_1; // x0
  __int64 instance; // x0
  __int64 v6; // x20
  __int64 v7; // x0
  __int64 *v8; // x8
  __int64 v9; // x20
  __int64 v10; // x2

  if ( (byte_490BC60 & 1) == 0 )
  {
    sub_184E408(&DCEBNDFOAMB_TypeInfo);
    sub_184E408(&GameController_TypeInfo);
    sub_184E408(&StringLiteral_44);
    sub_184E408(&StringLiteral_43);
    byte_490BC60 = 1;
  }
  DCEBNDFOAMB_TypeInfo_2 = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
                             *(_QWORD *)(a1 + 652),
                             *(unsigned int *)(a1 + 660),
                             0LL);
  if ( (DCEBNDFOAMB_TypeInfo_2 & 1) == 0 )
    return DCEBNDFOAMB_TypeInfo_2;
  if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo + 56) )
    ((void (*)(void))j_il2cpp_runtime_class_init_0)();
  if ( !byte_490BC6B )
  {
    sub_184E408(&DCEBNDFOAMB_TypeInfo);
    byte_490BC6B = 1;
  }
  DCEBNDFOAMB_TypeInfo = DCEBNDFOAMB_TypeInfo;
  if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo + 56) )
  {
    ((void (*)(void))j_il2cpp_runtime_class_init_0)();
    DCEBNDFOAMB_TypeInfo = DCEBNDFOAMB_TypeInfo;
  }
  if ( *(_BYTE *)(DCEBNDFOAMB_TypeInfo[23] + 97LL) )
  {
    if ( (CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
            *(_QWORD *)(a1 + 752),
            *(unsigned int *)(a1 + 760),
            0LL) & 1) != 0 )
      goto LABEL_48;
    DCEBNDFOAMB_TypeInfo = DCEBNDFOAMB_TypeInfo;
  }
  if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo + 56) )
    ((void (*)(void))j_il2cpp_runtime_class_init_0)();
  if ( !byte_490BC6B )
  {
    sub_184E408(&DCEBNDFOAMB_TypeInfo);
    byte_490BC6B = 1;
  }
  DCEBNDFOAMB_TypeInfo_1 = DCEBNDFOAMB_TypeInfo;
  if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo + 56) )
  {
    ((void (*)(void))j_il2cpp_runtime_class_init_0)();
    DCEBNDFOAMB_TypeInfo_1 = DCEBNDFOAMB_TypeInfo;
  }
  if ( !*(_BYTE *)(DCEBNDFOAMB_TypeInfo_1[23] + 97LL) )
  {
    if ( (CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
            *(_QWORD *)(a1 + 752),
            *(unsigned int *)(a1 + 760),
            0LL) & 1) != 0 )
    {
      DCEBNDFOAMB_TypeInfo_1 = DCEBNDFOAMB_TypeInfo;
      goto LABEL_22;
    }
LABEL_48:
    if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo + 56) )
      ((void (*)(void))j_il2cpp_runtime_class_init_0)();
    v7 = DCEBNDFOAMB__DJIBLEGCIFN(0LL);
    v8 = &StringLiteral_44;
    goto LABEL_51;
  }
LABEL_22:
  if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo_1 + 56) )
    ((void (*)(void))j_il2cpp_runtime_class_init_0)();
  if ( !byte_490BC6B )
  {
    sub_184E408(&DCEBNDFOAMB_TypeInfo);
    byte_490BC6B = 1;
  }
  DCEBNDFOAMB_TypeInfo_2 = (__int64)DCEBNDFOAMB_TypeInfo;
  if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo + 56) )
  {
    ((void (*)(void))j_il2cpp_runtime_class_init_0)();
    DCEBNDFOAMB_TypeInfo_2 = (__int64)DCEBNDFOAMB_TypeInfo;
  }
  if ( !*(_BYTE *)(*(_QWORD *)(DCEBNDFOAMB_TypeInfo_2 + 184) + 97LL) )
  {
    if ( !*(_DWORD *)(DCEBNDFOAMB_TypeInfo_2 + 224) )
      ((void (*)(void))j_il2cpp_runtime_class_init_0)();
    DCEBNDFOAMB_TypeInfo_2 = DCEBNDFOAMB__HNMKFNACEOP(0LL);
    if ( (DCEBNDFOAMB_TypeInfo_2 & 1) != 0 )
    {
      if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo + 56) )
        ((void (*)(void))j_il2cpp_runtime_class_init_0)();
      if ( DCEBNDFOAMB__DJIBLEGCIFN(0LL) )
      {
        if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo + 56) )
          ((void (*)(void))j_il2cpp_runtime_class_init_0)();
        instance = DCEBNDFOAMB__DJIBLEGCIFN(0LL);
        if ( !instance )
          goto LABEL_64;
        if ( *(_QWORD *)(instance + 32) )
        {
          if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo + 56) )
            ((void (*)(void))j_il2cpp_runtime_class_init_0)();
          instance = DCEBNDFOAMB__DJIBLEGCIFN(0LL);
          if ( !instance )
            goto LABEL_64;
          v6 = *(_QWORD *)(instance + 32);
          if ( !*((_DWORD *)GameController_TypeInfo + 56) )
            j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
          instance = GameController__get_instance(0LL);
          if ( !instance )
            goto LABEL_64;
          instance = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(
                       *(_QWORD *)(instance + 280),
                       0LL);
          if ( !v6 )
            goto LABEL_64;
          DCEBNDFOAMB_TypeInfo_2 = System_String__Equals_60658464(v6, instance, 0LL);
          if ( (DCEBNDFOAMB_TypeInfo_2 & 1) != 0 )
            return DCEBNDFOAMB_TypeInfo_2;
        }
      }
      if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo + 56) )
        j_il2cpp_runtime_class_init_0(DCEBNDFOAMB_TypeInfo);
      v7 = DCEBNDFOAMB__DJIBLEGCIFN(0LL);
      v8 = &StringLiteral_43;
LABEL_51:
      v9 = *v8;
      if ( v7 )
      {
        if ( !*((_DWORD *)DCEBNDFOAMB_TypeInfo + 56) )
          j_il2cpp_runtime_class_init_0(DCEBNDFOAMB_TypeInfo);
        instance = DCEBNDFOAMB__DJIBLEGCIFN(0LL);
        if ( instance )
        {
          v10 = *(_QWORD *)(instance + 32);
          return SFSController__SendWarn(a1, v9, v10);
        }
      }
      else
      {
        if ( !*((_DWORD *)GameController_TypeInfo + 56) )
          j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
        instance = GameController__get_instance(0LL);
        if ( instance )
        {
          v10 = CodeStage_AntiCheat_ObscuredTypes_ObscuredString__op_Implicit_26304656(*(_QWORD *)(instance + 280), 0LL);
          return SFSController__SendWarn(a1, v9, v10);
        }
      }
LABEL_64:
      sub_184E634(instance);
    }
  }
  return DCEBNDFOAMB_TypeInfo_2;
}